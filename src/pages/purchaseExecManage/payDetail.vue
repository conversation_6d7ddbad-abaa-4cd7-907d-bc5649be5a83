<template>
    <el-dialog
        title="付款详情"
        :visible.sync="visible"
        width="80%"
        :before-close="handleClose"
        :close-on-click-modal="false"
    >
        <div v-loading="loading">
            <el-form :model="billInfo" label-width="120px" size="small">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="是否预付款:">
                            <el-select
                                v-model="billInfo.pre_payment_flag"
                                placeholder="请选择是否预付款"
                                style="width: 100%"
                            >
                                <el-option label="是" :value="是" />
                                <el-option label="否" :value="否" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="公司名称:">
                            <span>{{ billInfo.company }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="单据类型:">
                            <span>{{ billInfo.bill_type }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="单据编号:">
                            <span>{{ billInfo.bill_no }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="单据日期:">
                            <el-date-picker
                                value-format="yyyy-MM-dd"
                                v-model="billInfo.bill_date"
                                type="date"
                                placeholder="请选择单据日期"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="供应商名称:">
                            <span>{{ billInfo.supplier }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="部门名称:">
                            <span>{{ billInfo.department }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="业务员名称:">
                            <span>{{ billInfo.salesman }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="付款银行账号:">
                            <span>{{ billInfo.payment_bank_account }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="付款银行名称:">
                            <span>{{ billInfo.payment_bank_name }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="收款银行账号:">
                            <el-input
                                v-model="billInfo.receipt_bank_account"
                                placeholder="请输入收款银行账号"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="收款银行名称:">
                            <el-input
                                v-model="billInfo.receipt_bank_name"
                                placeholder="请输入收款银行名称"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="结算方式:">
                            <span>{{ billInfo.settlement_method }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="币种:">
                            <span>{{ billInfo.currency }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="原币金额:">
                            <el-input
                                v-model="billInfo.original_amount"
                                :readonly="true"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="备注:">
                            <el-input
                                v-model="billInfo.remark"
                                type="textarea"
                                placeholder="请输入备注"
                                :autosize="{ minRows: 2 }"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>

            <div class="mt-20">
                <div class="table-title">付款单明细</div>
                <el-table
                    :data="billInfo.items || []"
                    border
                    style="width: 100%"
                >
                    <el-table-column
                        prop="order_no"
                        label="采购单号"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        prop="short_code"
                        label="简码"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        prop="cn_product_name"
                        label="中文品名"
                        align="center"
                        show-overflow-tooltip
                    ></el-table-column>
                    <el-table-column
                        prop="period"
                        label="期数"
                        align="center"
                    ></el-table-column>
                    <el-table-column label="原币金额" align="center">
                        <template slot-scope="scope">
                            <el-input
                                v-model.number="scope.row.original_amount"
                                @input="updateTotalAmount"
                            />
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>

        <span slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        orderId: {
            type: [Number, String],
            default: "",
        },
    },
    data() {
        return {
            loading: false,
            billInfo: {
                id: "",
                pre_payment_flag: "",
                company: "",
                bill_type: "",
                bill_no: "",
                bill_date: "",
                supplier: "",
                department: "",
                salesman: "",
                payment_bank_account: "",
                payment_bank_name: "",
                receipt_bank_account: "",
                receipt_bank_name: "",
                settlement_method: "",
                currency: "",
                original_amount: "",
                remark: "",
                items: [],
            },
        };
    },
    watch: {
        visible(val) {
            if (val && this.orderId) {
                this.getPaymentInfo();
            }
        },
    },
    methods: {
        getPaymentInfo() {
            this.loading = true;
            this.$request.caiGouPerform
                .getPaymentInfo({ id: this.orderId })
                .then((res) => {
                    if (res.data.error_code === 0 && res.data.data) {
                        this.billInfo =
                            res.data.data.bill_info || this.billInfo;
                        this.updateTotalAmount();
                    } else {
                        this.$message.error(
                            res.data.error_msg || "获取付款信息失败"
                        );
                    }
                })
                .catch((err) => {
                    console.error("获取付款信息失败", err);
                    this.$message.error("获取付款信息失败");
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        updateTotalAmount() {
            if (Array.isArray(this.billInfo.items)) {
                this.billInfo.original_amount = this.billInfo.items
                    .reduce((sum, item) => {
                        let val = parseFloat(item.original_amount);
                        return sum + (isNaN(val) ? 0 : val);
                    }, 0)
                    .toFixed(2);
            }
        },
        handleClose() {
            this.$emit("update:visible", false);
        },
        handleConfirm() {
            this.$request.caiGouPerform
                .sendFkApproval(this.billInfo)
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.$message.success("审批发起成功");
                        this.$emit("update:visible", false);
                    }
                })
                .catch((err) => {});
            // 这里可以添加提交表单的逻辑
            //   this.$message.success('保存成功');
            //   this.$emit('update:visible', false);
        },
    },
};
</script>

<style scoped>
.mt-20 {
    margin-top: 20px;
}
.table-title {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
}
.el-form-item {
    margin-bottom: 8px;
}
</style>
